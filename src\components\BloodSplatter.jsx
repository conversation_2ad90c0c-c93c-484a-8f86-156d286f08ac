import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const BloodSplatter = () => {
  const [splatters, setSplatters] = useState([]);

  useEffect(() => {
    const handleClick = (e) => {
      const newSplatter = {
        id: Date.now() + Math.random(),
        x: e.clientX,
        y: e.clientY,
        size: Math.random() * 100 + 50,
        rotation: Math.random() * 360,
        intensity: Math.random() * 0.8 + 0.2
      };

      setSplatters(prev => [...prev, newSplatter]);

      // Remove splatter after animation
      setTimeout(() => {
        setSplatters(prev => prev.filter(s => s.id !== newSplatter.id));
      }, 3000);
    };

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, []);

  return (
    <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 1000 }}>
      <AnimatePresence>
        {splatters.map(splatter => (
          <motion.div
            key={splatter.id}
            initial={{ 
              scale: 0, 
              opacity: 1,
              rotate: splatter.rotation 
            }}
            animate={{ 
              scale: 1, 
              opacity: [1, 0.8, 0],
              rotate: splatter.rotation + 45
            }}
            exit={{ 
              scale: 0.5, 
              opacity: 0 
            }}
            transition={{ 
              duration: 3,
              ease: "easeOut"
            }}
            style={{
              position: 'absolute',
              left: splatter.x - splatter.size / 2,
              top: splatter.y - splatter.size / 2,
              width: splatter.size,
              height: splatter.size,
              pointerEvents: 'none'
            }}
          >
            <svg
              width={splatter.size}
              height={splatter.size}
              viewBox="0 0 100 100"
              style={{
                filter: `blur(${Math.random() * 2}px) brightness(${splatter.intensity})`
              }}
            >
              {/* Main splatter */}
              <circle
                cx="50"
                cy="50"
                r="25"
                fill="#8B0000"
                opacity={splatter.intensity}
              />
              
              {/* Splatter drops */}
              {[...Array(8)].map((_, i) => {
                const angle = (i * 45) * (Math.PI / 180);
                const distance = 30 + Math.random() * 20;
                const dropSize = Math.random() * 8 + 3;
                const x = 50 + Math.cos(angle) * distance;
                const y = 50 + Math.sin(angle) * distance;
                
                return (
                  <circle
                    key={i}
                    cx={x}
                    cy={y}
                    r={dropSize}
                    fill="#4A0000"
                    opacity={splatter.intensity * 0.8}
                  />
                );
              })}
              
              {/* Small droplets */}
              {[...Array(12)].map((_, i) => {
                const angle = Math.random() * 360 * (Math.PI / 180);
                const distance = 40 + Math.random() * 30;
                const dropSize = Math.random() * 3 + 1;
                const x = 50 + Math.cos(angle) * distance;
                const y = 50 + Math.sin(angle) * distance;
                
                return (
                  <circle
                    key={`small-${i}`}
                    cx={x}
                    cy={y}
                    r={dropSize}
                    fill="#FF0000"
                    opacity={splatter.intensity * 0.6}
                  />
                );
              })}
            </svg>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default BloodSplatter;
