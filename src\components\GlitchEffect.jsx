import React, { useEffect, useState } from 'react';

const GlitchEffect = ({ children, intensity = 1, trigger = 'hover' }) => {
  const [isGlitching, setIsGlitching] = useState(false);
  const [glitchData, setGlitchData] = useState({
    offsetX: 0,
    offsetY: 0,
    scaleX: 1,
    scaleY: 1,
    hue: 0,
    contrast: 1
  });

  useEffect(() => {
    let interval;
    
    if (isGlitching) {
      interval = setInterval(() => {
        setGlitchData({
          offsetX: (Math.random() - 0.5) * 10 * intensity,
          offsetY: (Math.random() - 0.5) * 10 * intensity,
          scaleX: 1 + (Math.random() - 0.5) * 0.1 * intensity,
          scaleY: 1 + (Math.random() - 0.5) * 0.1 * intensity,
          hue: Math.random() * 360,
          contrast: 1 + (Math.random() - 0.5) * 0.5 * intensity
        });
      }, 50);
    } else {
      setGlitchData({
        offsetX: 0,
        offsetY: 0,
        scaleX: 1,
        scaleY: 1,
        hue: 0,
        contrast: 1
      });
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isGlitching, intensity]);

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      setIsGlitching(true);
    }
  };

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      setIsGlitching(false);
    }
  };

  const handleClick = () => {
    if (trigger === 'click') {
      setIsGlitching(true);
      setTimeout(() => setIsGlitching(false), 1000);
    }
  };

  useEffect(() => {
    if (trigger === 'auto') {
      const autoGlitch = () => {
        setIsGlitching(true);
        setTimeout(() => setIsGlitching(false), 200);
      };

      const interval = setInterval(autoGlitch, 3000 + Math.random() * 5000);
      return () => clearInterval(interval);
    }
  }, [trigger]);

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      style={{
        position: 'relative',
        display: 'inline-block',
        transform: `translate(${glitchData.offsetX}px, ${glitchData.offsetY}px) scale(${glitchData.scaleX}, ${glitchData.scaleY})`,
        filter: `hue-rotate(${glitchData.hue}deg) contrast(${glitchData.contrast})`,
        transition: isGlitching ? 'none' : 'all 0.3s ease'
      }}
    >
      {/* Main content */}
      <div style={{ position: 'relative', zIndex: 2 }}>
        {children}
      </div>

      {/* Glitch layers */}
      {isGlitching && (
        <>
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: 1,
              color: '#FF0000',
              transform: `translate(${glitchData.offsetX * 0.5}px, ${glitchData.offsetY * 0.5}px)`,
              opacity: 0.7,
              mixBlendMode: 'multiply'
            }}
          >
            {children}
          </div>
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: 0,
              color: '#00FF00',
              transform: `translate(${-glitchData.offsetX * 0.5}px, ${-glitchData.offsetY * 0.5}px)`,
              opacity: 0.7,
              mixBlendMode: 'multiply'
            }}
          >
            {children}
          </div>
        </>
      )}

      {/* Scan lines */}
      {isGlitching && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(255, 0, 0, 0.1) 2px, rgba(255, 0, 0, 0.1) 4px)',
            pointerEvents: 'none',
            zIndex: 3,
            animation: 'scanlines 0.1s linear infinite'
          }}
        />
      )}

      <style jsx>{`
        @keyframes scanlines {
          0% { transform: translateY(0); }
          100% { transform: translateY(4px); }
        }
      `}</style>
    </div>
  );
};

export default GlitchEffect;
