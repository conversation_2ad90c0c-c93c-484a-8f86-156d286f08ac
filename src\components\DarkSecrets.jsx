import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import HorrorCursor from './HorrorCursor';
import GlitchEffect from './GlitchEffect';
import BloodSplatter from './BloodSplatter';

const DarkSecrets = () => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [showJumpscare, setShowJumpscare] = useState(false);
  const [showRickRoll, setShowRickRoll] = useState(false);
  const [playScreamSound, setPlayScreamSound] = useState(false);
  const [eyesFollowing, setEyesFollowing] = useState({ x: 0, y: 0 });
  const [bloodDrops, setBloodDrops] = useState([]);
  const [screenShake, setScreenShake] = useState(false);
  const [flashEffect, setFlashEffect] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const audioRef = useRef(null);
  const rickRollRef = useRef(null);

  const words = ["So", "you", "wanna", "know", "about", "us", "huh?"];
  
  // Create horror sound effects using Web Audio API
  const createScreamSound = () => {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);

      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(1.0, audioContext.currentTime + 0.1);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
      console.log('Audio context not available');
    }
  };

  const createWhisperSound = () => {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();
    
    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(150, audioContext.currentTime);
    filter.frequency.setValueAtTime(300, audioContext.currentTime);
    filter.type = 'lowpass';
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 2);
  };

  useEffect(() => {
    // Word animation sequence
    const wordTimer = setInterval(() => {
      if (currentWordIndex < words.length - 1) {
        setCurrentWordIndex(prev => prev + 1);
        createWhisperSound();
      }
    }, 1500);

    // Random jumpscares
    const jumpscareTimer = setTimeout(() => {
      setShowJumpscare(true);
      setScreenShake(true);
      setFlashEffect(true);
      createScreamSound();
      setTimeout(() => {
        setShowJumpscare(false);
        setScreenShake(false);
        setFlashEffect(false);
      }, 500);
    }, Math.random() * 8000 + 5000);

    // Warning message at 8.5 seconds
    const warningTimer = setTimeout(() => {
      setShowWarning(true);
      setTimeout(() => setShowWarning(false), 1000);
    }, 8500);

    // Pre-Rick Roll scare at 9 seconds
    const preRickRollTimer = setTimeout(() => {
      setShowJumpscare(true);
      setScreenShake(true);
      setFlashEffect(true);
      createScreamSound();
      setTimeout(() => {
        setShowJumpscare(false);
        setScreenShake(false);
        setFlashEffect(false);
      }, 800);
    }, 9000);

    // Rick Roll after 10 seconds
    const rickRollTimer = setTimeout(() => {
      setShowRickRoll(true);

      // Try to go fullscreen
      setTimeout(() => {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen().catch(() => {
            console.log('Fullscreen not allowed');
          });
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen().catch(() => {});
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen().catch(() => {});
        }

        // Try to maximize volume
        try {
          if (rickRollRef.current) {
            const iframe = rickRollRef.current;
            // Send message to iframe to maximize volume
            iframe.contentWindow?.postMessage('{"event":"command","func":"setVolume","args":[100]}', '*');
            iframe.contentWindow?.postMessage('{"event":"command","func":"playVideo","args":[]}', '*');
          }
        } catch (error) {
          console.log('Could not control iframe volume');
        }
      }, 100);
    }, 10000);

    return () => {
      clearInterval(wordTimer);
      clearTimeout(jumpscareTimer);
      clearTimeout(warningTimer);
      clearTimeout(preRickRollTimer);
      clearTimeout(rickRollTimer);
    };
  }, [currentWordIndex]);

  useEffect(() => {
    const handleMouseMove = (e) => {
      const x = (e.clientX / window.innerWidth - 0.5) * 50;
      const y = (e.clientY / window.innerHeight - 0.5) * 50;
      setEyesFollowing({ x, y });
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Blood drip effect
  useEffect(() => {
    const bloodTimer = setInterval(() => {
      const newDrop = {
        id: Date.now(),
        x: Math.random() * window.innerWidth,
        delay: Math.random() * 2
      };
      setBloodDrops(prev => [...prev, newDrop]);
      
      setTimeout(() => {
        setBloodDrops(prev => prev.filter(drop => drop.id !== newDrop.id));
      }, 5000);
    }, 1000);

    return () => clearInterval(bloodTimer);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'radial-gradient(circle, #000000 0%, #1a0000 50%, #000000 100%)',
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      transform: screenShake ? 'translate(5px, 5px)' : 'none',
      transition: screenShake ? 'none' : 'transform 0.3s ease'
    }}>
      <HorrorCursor />
      <BloodSplatter />

      {/* Flash effect */}
      {flashEffect && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: '#FF0000',
          opacity: 0.8,
          zIndex: 9998,
          animation: 'flash 0.1s ease-in-out 5'
        }} />
      )}

      {/* Blood drops */}
      {bloodDrops.map(drop => (
        <motion.div
          key={drop.id}
          initial={{ y: -50, opacity: 1 }}
          animate={{ y: window.innerHeight + 50, opacity: 0 }}
          transition={{ duration: 3, delay: drop.delay, ease: "linear" }}
          style={{
            position: 'absolute',
            left: drop.x,
            top: 0,
            width: '4px',
            height: '20px',
            background: 'linear-gradient(180deg, #FF0000, #8B0000)',
            borderRadius: '2px',
            zIndex: 1
          }}
        />
      ))}

      {/* Floating eyes */}
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={i}
          animate={{
            x: [0, 30, -20, 0],
            y: [0, -20, 30, 0],
            scale: [1, 1.2, 0.8, 1]
          }}
          transition={{
            duration: 8 + i * 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{
            position: 'absolute',
            left: `${10 + i * 15}%`,
            top: `${20 + (i % 3) * 20}%`,
            fontSize: '4rem',
            zIndex: 2,
            filter: 'drop-shadow(0 0 20px #FF0000)'
          }}
        >
          <div style={{
            transform: `translate(${eyesFollowing.x}px, ${eyesFollowing.y}px)`,
            transition: 'transform 0.1s ease'
          }}>
            👁️
          </div>
        </motion.div>
      ))}

      {/* Main content */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 2 }}
        style={{
          textAlign: 'center',
          zIndex: 3,
          position: 'relative'
        }}
      >
        {/* Animated text sequence */}
        <div style={{
          fontSize: 'clamp(3rem, 8vw, 8rem)',
          fontFamily: 'Butcherman, cursive',
          color: '#FF0000',
          marginBottom: '3rem',
          minHeight: '200px',
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '1rem'
        }}>
          {words.map((word, index) => (
            <AnimatePresence key={index}>
              {index <= currentWordIndex && (
                <motion.span
                  initial={{ 
                    opacity: 0, 
                    scale: 0,
                    rotateX: 90,
                    y: 50
                  }}
                  animate={{ 
                    opacity: 1, 
                    scale: [0, 1.3, 1],
                    rotateX: 0,
                    y: 0
                  }}
                  transition={{ 
                    duration: 0.8,
                    ease: "easeOut"
                  }}
                  style={{
                    display: 'inline-block',
                    textShadow: `
                      0 0 20px #FF0000,
                      0 0 40px #8B0000,
                      0 0 60px #FF0000
                    `,
                    filter: 'drop-shadow(0 0 10px #000)'
                  }}
                >
                  <GlitchEffect intensity={2} trigger="auto">
                    {word}
                  </GlitchEffect>
                </motion.span>
              )}
            </AnimatePresence>
          ))}
        </div>

        {/* Creepy message after all words appear */}
        {currentWordIndex >= words.length - 1 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2, duration: 1 }}
            style={{
              fontSize: '1.5rem',
              fontFamily: 'Eater, cursive',
              color: '#F5F5DC',
              marginTop: '2rem',
              textAlign: 'center'
            }}
          >
            <motion.p
              animate={{
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 3,
                repeat: Infinity
              }}
            >
              We are the shadows that watch you sleep...
            </motion.p>
            <motion.p
              animate={{
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: 1
              }}
              style={{ marginTop: '1rem' }}
            >
              The whispers in your darkest dreams...
            </motion.p>
          </motion.div>
        )}
      </motion.div>

      {/* Warning message */}
      <AnimatePresence>
        {showWarning && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.3 }}
            style={{
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              background: 'rgba(139, 0, 0, 0.95)',
              border: '3px solid #FF0000',
              borderRadius: '15px',
              padding: '2rem',
              zIndex: 9998,
              textAlign: 'center',
              boxShadow: '0 0 50px rgba(255, 0, 0, 0.8)'
            }}
          >
            <motion.h2
              animate={{
                scale: [1, 1.1, 1],
                color: ['#FF0000', '#FFFFFF', '#FF0000']
              }}
              transition={{
                duration: 0.5,
                repeat: Infinity
              }}
              style={{
                fontFamily: 'Butcherman, cursive',
                fontSize: '2rem',
                margin: 0,
                textShadow: '0 0 20px #FF0000'
              }}
            >
              ⚠️ WARNING ⚠️
            </motion.h2>
            <p style={{
              fontFamily: 'Eater, cursive',
              fontSize: '1.2rem',
              color: '#F5F5DC',
              margin: '1rem 0 0 0'
            }}>
              YOU HAVE BEEN MARKED...
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Jumpscare */}
      <AnimatePresence>
        {showJumpscare && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.1 }}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100vw',
              height: '100vh',
              background: '#000',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 9999
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 0.1,
                repeat: 5
              }}
              style={{
                fontSize: '20rem',
                filter: 'drop-shadow(0 0 50px #FF0000)'
              }}
            >
              💀
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Rick Roll */}
      <AnimatePresence>
        {showRickRoll && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100vw',
              height: '100vh',
              background: '#000',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10000
            }}
          >
            <iframe
              ref={rickRollRef}
              width="100%"
              height="100%"
              src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=0&controls=0&loop=1&playlist=dQw4w9WgXcQ&start=0&rel=0&modestbranding=1&fs=1"
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
              style={{
                border: 'none'
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Background static */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: `
          repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(255, 0, 0, 0.02) 2px,
            rgba(255, 0, 0, 0.02) 4px
          )
        `,
        animation: 'staticNoise 0.1s linear infinite',
        pointerEvents: 'none',
        zIndex: 0
      }} />
    </div>
  );
};

export default DarkSecrets;
