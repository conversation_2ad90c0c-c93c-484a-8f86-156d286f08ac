import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import HorrorCursor from "./HorrorCursor";
import GlitchEffect from "./GlitchEffect";
import BloodSplatter from "./BloodSplatter";

const VoidWalker = () => {
  const [eyePosition, setEyePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      const x = (e.clientX / window.innerWidth - 0.5) * 20;
      const y = (e.clientY / window.innerHeight - 0.5) * 20;
      setEyePosition({ x, y });
    };

    document.addEventListener("mousemove", handleMouseMove);
    return () => document.removeEventListener("mousemove", handleMouseMove);
  }, []);

  return (
    <div
      className="horror-container"
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "radial-gradient(circle, #1a0000 0%, #000000 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      <HorrorCursor />
      <BloodSplatter />

      {/* Floating skulls */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          animate={{
            x: [0, 50, -30, 0],
            y: [0, -30, 50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 15 + i * 3,
            repeat: Infinity,
            ease: "linear",
          }}
          style={{
            position: "absolute",
            left: `${10 + i * 10}%`,
            top: `${15 + (i % 3) * 25}%`,
            fontSize: "3rem",
            opacity: 0.2,
            zIndex: 1,
          }}
        >
          💀
        </motion.div>
      ))}

      <motion.div
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1, ease: "easeOut" }}
        style={{
          textAlign: "center",
          zIndex: 2,
          position: "relative",
        }}
      >
        <GlitchEffect intensity={3} trigger="auto">
          <motion.h1
            className="glitch-text"
            data-text="404"
            animate={{
              textShadow: [
                "0 0 20px #8B0000",
                "0 0 40px #FF0000, 0 0 60px #8B0000",
                "0 0 20px #8B0000",
              ],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            style={{
              fontSize: "clamp(4rem, 15vw, 12rem)",
              fontFamily: "Butcherman, cursive",
              color: "#FF0000",
              marginBottom: "2rem",
              lineHeight: 1,
            }}
          >
            404
          </motion.h1>
        </GlitchEffect>

        <motion.div
          animate={{
            scale: [1, 1.05, 1],
            rotate: [0, 1, -1, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          style={{
            fontSize: "8rem",
            marginBottom: "2rem",
          }}
        >
          <div
            style={{
              position: "relative",
              display: "inline-block",
            }}
          >
            👁️
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: `translate(calc(-50% + ${eyePosition.x}px), calc(-50% + ${eyePosition.y}px))`,
                fontSize: "2rem",
                transition: "transform 0.1s ease",
              }}
            >
              ⚫
            </div>
          </div>
        </motion.div>

        <GlitchEffect intensity={2} trigger="hover">
          <motion.h2
            className="glitch-text"
            data-text="LOST IN THE VOID"
            whileHover={{
              scale: 1.1,
              color: "#FF0000",
            }}
            style={{
              fontSize: "clamp(1.5rem, 5vw, 3rem)",
              fontFamily: "Eater, cursive",
              marginBottom: "2rem",
              color: "#8B0000",
            }}
          >
            LOST IN THE VOID
          </motion.h2>
        </GlitchEffect>

        <motion.p
          animate={{
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          style={{
            fontSize: "1.2rem",
            fontFamily: "Metal Mania, cursive",
            color: "#F5F5DC",
            maxWidth: "600px",
            lineHeight: 1.6,
            marginBottom: "3rem",
          }}
        >
          The shadows have consumed this path... The digital realm knows not of
          your destination. Perhaps you seek the forbidden knowledge hidden
          within our sacred scrolls?
        </motion.p>

        <motion.div
          animate={{
            y: [0, -10, 0],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          style={{
            marginTop: "3rem",
            fontSize: "0.9rem",
            fontStyle: "italic",
            color: "#8B0000",
            opacity: 0.8,
          }}
        >
          "In the darkness, all paths lead to the same destination..."
        </motion.div>
      </motion.div>

      {/* Background static effect */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: `
          repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(255, 0, 0, 0.02) 2px,
            rgba(255, 0, 0, 0.02) 4px
          )
        `,
          animation: "staticNoise 0.1s linear infinite",
          pointerEvents: "none",
          zIndex: 0,
        }}
      />
    </div>
  );
};

export default VoidWalker;
