import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import HorrorCursor from './HorrorCursor';
import GlitchEffect from './GlitchEffect';
import BloodSplatter from './BloodSplatter';

const PrivacyPolicy = () => {
  const [bloodDrops, setBloodDrops] = useState([]);
  const [visibleSections, setVisibleSections] = useState(new Set());

  useEffect(() => {
    // Create blood drips
    const createBloodDrip = () => {
      const newDrop = {
        id: Math.random(),
        left: Math.random() * 100,
        delay: Math.random() * 2
      };
      setBloodDrops(prev => [...prev, newDrop]);
      
      setTimeout(() => {
        setBloodDrops(prev => prev.filter(drop => drop.id !== newDrop.id));
      }, 3000);
    };

    const interval = setInterval(createBloodDrip, 800);
    return () => clearInterval(interval);
  }, []);

  const handleSectionHover = (sectionId) => {
    setVisibleSections(prev => new Set([...prev, sectionId]));
  };

  const glitchVariants = {
    initial: { opacity: 0, y: 50 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.8, ease: "easeOut" }
    },
    hover: {
      scale: 1.05,
      textShadow: "0 0 20px #8B0000, 0 0 30px #8B0000, 0 0 40px #8B0000",
      transition: { duration: 0.3 }
    }
  };

  return (
    <div className="horror-container" style={{
      minHeight: '100vh',
      padding: '2rem',
      position: 'relative',
      background: 'linear-gradient(180deg, #000000 0%, #1a0000 50%, #000000 100%)'
    }}>
      <HorrorCursor />
      <BloodSplatter />
      {/* Blood drips */}
      {bloodDrops.map(drop => (
        <div
          key={drop.id}
          className="blood-drip"
          style={{
            left: `${drop.left}%`,
            animationDelay: `${drop.delay}s`
          }}
        />
      ))}

      {/* Floating particles */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        background: `
          radial-gradient(2px 2px at 20px 30px, #8B0000, transparent),
          radial-gradient(2px 2px at 40px 70px, #4A0000, transparent),
          radial-gradient(1px 1px at 90px 40px, #8B0000, transparent),
          radial-gradient(1px 1px at 130px 80px, #4A0000, transparent),
          radial-gradient(2px 2px at 160px 30px, #8B0000, transparent)
        `,
        backgroundRepeat: 'repeat',
        backgroundSize: '200px 100px',
        animation: 'float 20s linear infinite',
        opacity: 0.3
      }} />

      <motion.div
        initial="initial"
        animate="animate"
        style={{ maxWidth: '800px', margin: '0 auto', position: 'relative', zIndex: 2 }}
      >
        <GlitchEffect intensity={2} trigger="auto">
          <motion.h1
            className="glitch-text"
            data-text="Privacy Policy for faxrn"
            variants={glitchVariants}
            whileHover="hover"
            style={{
              textAlign: 'center',
              marginBottom: '2rem',
              fontSize: 'clamp(2rem, 6vw, 4rem)'
            }}
          >
            Privacy Policy for faxrn
          </motion.h1>
        </GlitchEffect>

        <motion.p 
          variants={glitchVariants}
          style={{ 
            textAlign: 'center', 
            fontSize: '1.2rem',
            color: '#8B0000',
            marginBottom: '3rem'
          }}
        >
          <strong>Last Updated: July 4, 2025</strong>
        </motion.p>

        <motion.div
          variants={glitchVariants}
          className="reveal-text"
          onMouseEnter={() => handleSectionHover('intro')}
          style={{ marginBottom: '2rem' }}
        >
          <p style={{ fontSize: '1.1rem', lineHeight: '1.8' }}>
            Welcome to faxrn, the preeminent forums app for discourse in shadows yet undefined. 
            This Privacy Policy (the "Policy") governs the manner in which faxrn (henceforth the "Website") 
            harvests, employs, maintains, and discloses the essence of those who dare to traverse its digital halls 
            (the "Users"). By accessing or utilizing the Website, you irrevocably bind yourself to this Policy 
            and the accompanying Terms of Service. Should you dissent from these covenants, the gates of the 
            Website shall remain barred to you.
          </p>
        </motion.div>

        <hr style={{ 
          border: 'none', 
          height: '2px', 
          background: 'linear-gradient(90deg, transparent, #8B0000, transparent)',
          margin: '3rem 0'
        }} />

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('collection') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('collection')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="1. Information Collection">
            1. Information Collection
          </h2>
          <p>
            We harvest the whispers of your digital wanderings, the echoes of your keystrokes, 
            and the shadows of your thoughts. This includes, but is not limited to:
          </p>
          <ul style={{ 
            listStyle: 'none', 
            padding: 0,
            color: '#F5F5DC'
          }}>
            <li style={{ marginBottom: '1rem', paddingLeft: '2rem', position: 'relative' }}>
              <span style={{ position: 'absolute', left: 0, color: '#8B0000' }}>🩸</span>
              <strong>Personal Tokens:</strong> Your name, electronic missive address, telephonic cipher, 
              dwelling place, and other sigils of identity you may offer unto us.
            </li>
            <li style={{ marginBottom: '1rem', paddingLeft: '2rem', position: 'relative' }}>
              <span style={{ position: 'absolute', left: 0, color: '#8B0000' }}>💀</span>
              <strong>Trails of Passage:</strong> The paths you tread within the Website, the relics you 
              interact with, and the duration of your sojourns.
            </li>
            <li style={{ marginBottom: '1rem', paddingLeft: '2rem', position: 'relative' }}>
              <span style={{ position: 'absolute', left: 0, color: '#8B0000' }}>👁️</span>
              <strong>Device Lore:</strong> The nature of your tools of access, including their type, 
              operating system, unique identifiers, and the arcane signatures of your network.
            </li>
            <li style={{ marginBottom: '1rem', paddingLeft: '2rem', position: 'relative' }}>
              <span style={{ position: 'absolute', left: 0, color: '#8B0000' }}>🕷️</span>
              <strong>Cookies and Beacons:</strong> We employ these silent sentinels to track your movements 
              and preferences, embedding them within your device to better understand your desires and fears.
            </li>
            <li style={{ marginBottom: '1rem', paddingLeft: '2rem', position: 'relative' }}>
              <span style={{ position: 'absolute', left: 0, color: '#8B0000' }}>👻</span>
              <strong>Whispers from Beyond:</strong> Data gleaned from third parties, including social media 
              oracles and data brokers, to enrich our knowledge of your being.
            </li>
          </ul>
          <p style={{ 
            fontStyle: 'italic', 
            color: '#8B0000',
            textShadow: '0 0 10px #8B0000'
          }}>
            You acknowledge that by providing any information to the Website, you consent to its collection 
            and use as described herein, and that such information may be stored in realms both known and unknown.
          </p>
        </motion.section>

        <hr style={{ 
          border: 'none', 
          height: '2px', 
          background: 'linear-gradient(90deg, transparent, #8B0000, transparent)',
          margin: '3rem 0'
        }} />

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('use') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('use')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="2. Use of Information">
            2. Use of Information
          </h2>
          <p>
            Your data fuels the engines of our arcane machinations, powering the unseen mechanisms 
            that drive the Website's dark heart. Specifically, we may use your information to:
          </p>
          <div style={{ 
            display: 'grid', 
            gap: '1rem',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))'
          }}>
            {[
              { icon: '🔮', title: 'Tailor the Experience', desc: 'Craft a bespoke journey through the Website, attuned to your predilections and past deeds.' },
              { icon: '📜', title: 'Commune with You', desc: 'Send missives, updates, or promotional incantations, though you may petition to silence these voices.' },
              { icon: '⚗️', title: 'Refine the Website', desc: 'Analyze patterns and behaviors to enhance the Website\'s offerings and fortify its defenses.' },
              { icon: '🔍', title: 'Divine Insights', desc: 'Conduct research, perform analytics, and generate reports that may be shared with entities beyond the veil.' },
              { icon: '⚖️', title: 'Enforce Our Will', desc: 'Verify your identity, prevent fraud, and ensure compliance with the edicts set forth in the Terms of Service.' },
              { icon: '💰', title: 'Barter and Trade', desc: 'Utilize your data in commerce, including but not limited to targeted advertising, data monetization, and other ventures that serve our inscrutable purposes.' }
            ].map((item, index) => (
              <motion.div
                key={index}
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: '0 0 20px rgba(139, 0, 0, 0.5)'
                }}
                style={{
                  padding: '1.5rem',
                  border: '1px solid #8B0000',
                  borderRadius: '8px',
                  background: 'rgba(26, 0, 0, 0.3)',
                  backdropFilter: 'blur(5px)'
                }}
              >
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{item.icon}</div>
                <h3 style={{ color: '#8B0000', marginBottom: '0.5rem' }}>{item.title}</h3>
                <p style={{ fontSize: '0.9rem' }}>{item.desc}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        <hr style={{
          border: 'none',
          height: '2px',
          background: 'linear-gradient(90deg, transparent, #8B0000, transparent)',
          margin: '3rem 0'
        }} />

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('sharing') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('sharing')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="3. Sharing of Information">
            3. Sharing of Information
          </h2>
          <p style={{ color: '#8B0000', fontWeight: 'bold', fontSize: '1.1rem' }}>
            We may cast your data into the void, where it may be seized by unseen hands or bartered
            with entities beyond mortal ken.
          </p>
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            marginTop: '2rem'
          }}>
            {[
              'Allies and Vassals', 'Merchants and Peddlers', 'Keepers of Order',
              'Successors and Heirs', 'Shadowed Figures'
            ].map((entity, index) => (
              <motion.div
                key={index}
                whileHover={{
                  rotate: [0, -2, 2, 0],
                  transition: { duration: 0.3 }
                }}
                style={{
                  padding: '1rem',
                  border: '2px solid #8B0000',
                  borderRadius: '50%',
                  background: 'radial-gradient(circle, #1a0000, #000000)',
                  textAlign: 'center',
                  minWidth: '120px',
                  minHeight: '120px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.9rem',
                  fontWeight: 'bold'
                }}
              >
                {entity}
              </motion.div>
            ))}
          </div>
        </motion.section>

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('security') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('security')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="4. Data Security">
            4. Data Security
          </h2>
          <div style={{
            background: 'linear-gradient(45deg, rgba(139, 0, 0, 0.1), rgba(26, 0, 0, 0.3))',
            padding: '2rem',
            borderRadius: '10px',
            border: '1px solid #8B0000',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(139, 0, 0, 0.1) 10px, rgba(139, 0, 0, 0.1) 20px)',
              pointerEvents: 'none'
            }} />
            <p style={{ position: 'relative', zIndex: 1 }}>
              We employ wards and sigils to guard your data against unbidden hands, including encryption,
              access controls, and other mystical protections. However, no spell can ward off all perils,
              and the digital realm is fraught with dangers unseen.
            </p>
            <p style={{
              position: 'relative',
              zIndex: 1,
              color: '#8B0000',
              fontWeight: 'bold',
              textAlign: 'center',
              marginTop: '1rem'
            }}>
              ⚠️ WE OFFER NO GUARANTEE OF ABSOLUTE SANCTUARY ⚠️
            </p>
          </div>
        </motion.section>

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('rights') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('rights')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="5. User Rights">
            5. User Rights
          </h2>
          <p>
            You may petition us to glimpse the data we hold, to amend inaccuracies, or to request its erasure.
            However, the veil may not lift easily, and we reserve the right to deny such requests if they
            conflict with our interests.
          </p>
        </motion.section>

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('contact') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('contact')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="8. Contact Information">
            8. Contact Information
          </h2>
          <div style={{
            background: 'rgba(139, 0, 0, 0.2)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <p><strong>Electronic Missive:</strong> <EMAIL></p>
            <p><strong>Postal Scroll:</strong> faxrn, 666 Shadow Lane, Suite 13, San Francisco, CA 94123</p>
          </div>
        </motion.section>

        <motion.section
          variants={glitchVariants}
          className={`reveal-text ${visibleSections.has('cryptic') ? 'visible' : ''}`}
          onMouseEnter={() => handleSectionHover('cryptic')}
          style={{ marginBottom: '3rem' }}
        >
          <h2 className="glitch-text" data-text="9. Cryptic Clause">
            9. Cryptic Clause
          </h2>
          <div style={{
            background: 'radial-gradient(circle, rgba(139, 0, 0, 0.3), rgba(0, 0, 0, 0.8))',
            padding: '2rem',
            borderRadius: '50%',
            textAlign: 'center',
            border: '3px solid #8B0000',
            animation: 'pulse 2s ease-in-out infinite'
          }}>
            <p style={{ fontStyle: 'italic', color: '#F5F5DC' }}>
              This Policy, like the Terms of Service, is bound to the cycles of the moon.
              In its waxing, your data may be more vulnerable; in its waning, it may be more secure.
              Consult the heavens to discern the safety of your essence.
            </p>
          </div>
        </motion.section>

        <motion.div
          variants={glitchVariants}
          style={{
            textAlign: 'center',
            padding: '3rem',
            background: 'linear-gradient(45deg, rgba(139, 0, 0, 0.3), rgba(26, 0, 0, 0.5))',
            borderRadius: '10px',
            border: '2px solid #8B0000',
            marginTop: '3rem'
          }}
        >
          <p style={{
            fontSize: '1.2rem',
            fontWeight: 'bold',
            color: '#8B0000'
          }}>
            By stepping into the Website, you affirm that you have peered into this Policy,
            grasped its weight, and chained yourself to its will. Refuse it, and the abyss awaits.
          </p>
          <p style={{
            fontSize: '0.9rem',
            fontStyle: 'italic',
            marginTop: '1rem',
            color: '#F5F5DC'
          }}>
            <strong>Note:</strong> This tome is a jesting shadow, unfit for true law.
          </p>
        </motion.div>
      </motion.div>

      <style jsx>{`
        @keyframes float {
          0% { transform: translateY(0px) translateX(0px); }
          33% { transform: translateY(-10px) translateX(5px); }
          66% { transform: translateY(5px) translateX(-5px); }
          100% { transform: translateY(0px) translateX(0px); }
        }
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
      `}</style>
    </div>
  );
};

export default PrivacyPolicy;
