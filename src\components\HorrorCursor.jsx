import React, { useEffect, useState } from 'react';

const HorrorCursor = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isClicking, setIsClicking] = useState(false);
  const [trail, setTrail] = useState([]);

  useEffect(() => {
    const updatePosition = (e) => {
      const newPos = { x: e.clientX, y: e.clientY };
      setPosition(newPos);
      
      // Add to trail
      setTrail(prev => {
        const newTrail = [...prev, { ...newPos, id: Date.now() }];
        return newTrail.slice(-10); // Keep only last 10 positions
      });
    };

    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);

    document.addEventListener('mousemove', updatePosition);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', updatePosition);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  return (
    <>
      {/* Cursor trail */}
      {trail.map((pos, index) => (
        <div
          key={pos.id}
          style={{
            position: 'fixed',
            left: pos.x - 5,
            top: pos.y - 5,
            width: '10px',
            height: '10px',
            background: `rgba(139, 0, 0, ${(index + 1) / trail.length * 0.5})`,
            borderRadius: '50%',
            pointerEvents: 'none',
            zIndex: 9998,
            transition: 'opacity 0.3s ease'
          }}
        />
      ))}
      
      {/* Main cursor */}
      <div
        style={{
          position: 'fixed',
          left: position.x - 15,
          top: position.y - 15,
          width: '30px',
          height: '30px',
          pointerEvents: 'none',
          zIndex: 9999,
          transition: 'transform 0.1s ease',
          transform: isClicking ? 'scale(1.5)' : 'scale(1)'
        }}
      >
        <svg
          width="30"
          height="30"
          viewBox="0 0 30 30"
          style={{
            filter: 'drop-shadow(0 0 5px #8B0000)',
            animation: 'cursorPulse 2s ease-in-out infinite'
          }}
        >
          <path
            d="M15 2 L19 12 L26 8 L21 15 L28 19 L18 22 L21 30 L15 25 L9 30 L12 22 L2 19 L9 15 L4 8 L11 12 Z"
            fill="#FF0000"
            stroke="#000"
            strokeWidth="1"
          />
          <circle cx="15" cy="15" r="3" fill="#000" />
        </svg>
      </div>

      <style jsx>{`
        @keyframes cursorPulse {
          0%, 100% { filter: drop-shadow(0 0 5px #8B0000); }
          50% { filter: drop-shadow(0 0 15px #FF0000) drop-shadow(0 0 25px #8B0000); }
        }
      `}</style>
    </>
  );
};

export default HorrorCursor;
