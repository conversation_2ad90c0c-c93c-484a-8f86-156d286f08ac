@import url('https://fonts.googleapis.com/css2?family=Creepster&family=Nosifer&family=Butcherman&family=Eater&family=Metal+Mania&display=swap');

:root {
  --blood-red: #8B0000;
  --dark-red: #4A0000;
  --black: #000000;
  --dark-gray: #1a1a1a;
  --bone-white: #F5F5DC;
  --decay-green: #228B22;
  --shadow-purple: #301934;
  --rust-orange: #B7410E;

  font-family: 'Creepster', 'Nosifer', cursive;
  line-height: 1.2;
  font-weight: 400;

  color-scheme: dark;
  color: var(--bone-white);
  background: linear-gradient(45deg, var(--black), var(--dark-red), var(--shadow-purple));
  background-size: 400% 400%;
  animation: bloodFlow 8s ease-in-out infinite;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="%23FF0000" stroke="%23000" stroke-width="2"/><circle cx="10" cy="10" r="2" fill="%23000"/></svg>'), auto;
}

@keyframes bloodFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes drip {
  0% { transform: translateY(-100px); opacity: 0; }
  10% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.glitch-text {
  position: relative;
  color: var(--bone-white);
  font-family: 'Nosifer', cursive;
  text-shadow:
    0.05em 0 0 var(--blood-red),
    -0.05em -0.025em 0 var(--decay-green),
    0.025em 0.05em 0 var(--rust-orange);
  animation: glitch 0.3s infinite;
}

.glitch-text:before,
.glitch-text:after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text:before {
  animation: glitch 0.3s infinite;
  color: var(--blood-red);
  z-index: -1;
}

.glitch-text:after {
  animation: glitch 0.3s infinite reverse;
  color: var(--decay-green);
  z-index: -2;
}

.blood-drip {
  position: fixed;
  width: 2px;
  height: 20px;
  background: linear-gradient(to bottom, var(--blood-red), var(--dark-red));
  border-radius: 0 0 50% 50%;
  animation: drip 3s linear infinite;
  z-index: 1000;
}

.horror-cursor {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><path d="M16 2 L20 14 L28 10 L22 16 L30 20 L18 24 L22 32 L16 26 L10 32 L14 24 L2 20 L10 16 L4 10 L12 14 Z" fill="%23FF0000" stroke="%23000" stroke-width="1"/></svg>'), auto !important;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
  background: var(--black);
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, var(--blood-red) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, var(--shadow-purple) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, var(--dark-red) 0%, transparent 50%);
  opacity: 0.3;
  z-index: -1;
  animation: flicker 4s ease-in-out infinite;
}

* {
  box-sizing: border-box;
  cursor: inherit;
}

a {
  font-weight: 600;
  color: var(--blood-red);
  text-decoration: none;
  text-shadow: 0 0 10px var(--blood-red);
  transition: all 0.3s ease;
  font-family: 'Metal Mania', cursive;
}

a:hover {
  color: var(--bone-white);
  text-shadow:
    0 0 20px var(--blood-red),
    0 0 30px var(--blood-red),
    0 0 40px var(--blood-red);
  animation: shake 0.5s ease-in-out infinite;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Butcherman', cursive;
  color: var(--bone-white);
  text-shadow:
    2px 2px 0px var(--blood-red),
    4px 4px 0px var(--dark-red),
    6px 6px 10px rgba(0,0,0,0.8);
  margin: 1em 0;
  line-height: 1.2;
}

h1 {
  font-size: clamp(2rem, 5vw, 4rem);
  animation: glitch 2s ease-in-out infinite;
}

h2 {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-family: 'Eater', cursive;
}

p {
  font-family: 'Metal Mania', cursive;
  color: var(--bone-white);
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
  margin: 1em 0;
}

.reveal-text {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.reveal-text:hover,
.reveal-text.visible {
  opacity: 1;
  transform: translateY(0);
  text-shadow:
    0 0 10px var(--blood-red),
    0 0 20px var(--blood-red),
    0 0 30px var(--blood-red);
}

.horror-container {
  position: relative;
  z-index: 1;
}

/* Hide default cursor */
* {
  cursor: none !important;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--black);
  border: 1px solid var(--blood-red);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--blood-red), var(--dark-red));
  border-radius: 6px;
  border: 1px solid var(--black);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--dark-red), var(--blood-red));
}

/* Selection styling */
::selection {
  background: var(--blood-red);
  color: var(--bone-white);
  text-shadow: 0 0 10px var(--bone-white);
}

/* Additional horror animations */
@keyframes creepyFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(-5px) rotate(-1deg); }
  75% { transform: translateY(-15px) rotate(0.5deg); }
}

@keyframes bloodPulse {
  0%, 100% {
    box-shadow: 0 0 5px var(--blood-red);
    border-color: var(--blood-red);
  }
  50% {
    box-shadow: 0 0 20px var(--blood-red), 0 0 30px var(--dark-red);
    border-color: var(--dark-red);
  }
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px) rotateX(90deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
  }
}

@keyframes staticNoise {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.1; }
}

@keyframes flash {
  0%, 100% { opacity: 0; }
  50% { opacity: 0.8; }
}

@keyframes screenShake {
  0% { transform: translate(0); }
  10% { transform: translate(-5px, -5px); }
  20% { transform: translate(5px, -5px); }
  30% { transform: translate(-5px, 5px); }
  40% { transform: translate(5px, 5px); }
  50% { transform: translate(-5px, -5px); }
  60% { transform: translate(5px, -5px); }
  70% { transform: translate(-5px, 5px); }
  80% { transform: translate(5px, 5px); }
  90% { transform: translate(-5px, -5px); }
  100% { transform: translate(0); }
}

.horror-text-reveal {
  animation: textReveal 0.8s ease-out forwards;
}

.blood-border {
  border: 2px solid var(--blood-red);
  animation: bloodPulse 3s ease-in-out infinite;
}

.creepy-float {
  animation: creepyFloat 6s ease-in-out infinite;
}

.static-overlay {
  position: relative;
}

.static-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    );
  animation: staticNoise 0.1s linear infinite;
  pointer-events: none;
}

@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  p { font-size: 0.9rem; }

  .horror-container {
    padding: 1rem !important;
  }
}
