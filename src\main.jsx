import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './index.css'
import CrypticScrolls from './components/CrypticScrolls.jsx'
import BloodRitual from './components/BloodRitual.jsx'
import VoidWalker from './components/VoidWalker.jsx'
import DarkSecrets from './components/DarkSecrets.jsx'

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Router>
      <Routes>
        <Route path="/privacy" element={<CrypticScrolls />} />
        <Route path="/terms" element={<BloodRitual />} />
        <Route path="/about" element={<DarkSecrets />} />
        <Route path="*" element={<VoidWalker />} />
      </Routes>
    </Router>
  </StrictMode>,
)
