import React, { useEffect, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import HorrorCursor from './HorrorCursor';
import GlitchEffect from './GlitchEffect';
import BloodSplatter from './BloodSplatter';

const BloodRitual = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [bloodSplatters, setBloodSplatters] = useState([]);
  const [glitchIntensity, setGlitchIntensity] = useState(0);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    const handleClick = (e) => {
      const newSplatter = {
        id: Math.random(),
        x: e.clientX,
        y: e.clientY,
        size: Math.random() * 50 + 20
      };
      setBloodSplatters(prev => [...prev, newSplatter]);
      
      setTimeout(() => {
        setBloodSplatters(prev => prev.filter(splatter => splatter.id !== newSplatter.id));
      }, 5000);
    };

    const glitchInterval = setInterval(() => {
      setGlitchIntensity(Math.random());
    }, 100);

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('click', handleClick);
      clearInterval(glitchInterval);
    };
  }, []);

  const psychoVariants = {
    initial: { opacity: 0, scale: 0.8, rotate: -5 },
    animate: { 
      opacity: 1, 
      scale: 1, 
      rotate: 0,
      transition: { 
        duration: 1,
        ease: "easeOut",
        staggerChildren: 0.1
      }
    },
    hover: {
      scale: 1.1,
      rotate: [0, -2, 2, 0],
      textShadow: [
        "0 0 20px #8B0000",
        "0 0 40px #FF0000, 0 0 60px #8B0000",
        "0 0 20px #8B0000"
      ],
      transition: { duration: 0.5, repeat: Infinity, repeatType: "reverse" }
    }
  };

  const bloodTrail = {
    position: 'fixed',
    left: mousePosition.x - 10,
    top: mousePosition.y - 10,
    width: '20px',
    height: '20px',
    background: 'radial-gradient(circle, #FF0000, #8B0000)',
    borderRadius: '50%',
    pointerEvents: 'none',
    zIndex: 9999,
    opacity: 0.7,
    transition: 'all 0.1s ease'
  };

  return (
    <div 
      ref={containerRef}
      className="horror-container" 
      style={{ 
        minHeight: '100vh', 
        padding: '2rem',
        position: 'relative',
        background: `
          linear-gradient(45deg, #000000 0%, #1a0000 25%, #4a0000 50%, #1a0000 75%, #000000 100%),
          radial-gradient(circle at 30% 20%, rgba(255, 0, 0, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 70% 80%, rgba(139, 0, 0, 0.4) 0%, transparent 50%)
        `,
        backgroundSize: '400% 400%, 100% 100%, 100% 100%',
        animation: 'psychoBackground 6s ease-in-out infinite',
        overflow: 'hidden'
      }}
    >
      <HorrorCursor />
      <BloodSplatter />
      {/* Custom cursor trail */}
      <div style={bloodTrail} />

      {/* Blood splatters */}
      {bloodSplatters.map(splatter => (
        <motion.div
          key={splatter.id}
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 1, opacity: 0 }}
          transition={{ duration: 2 }}
          style={{
            position: 'fixed',
            left: splatter.x - splatter.size / 2,
            top: splatter.y - splatter.size / 2,
            width: splatter.size,
            height: splatter.size,
            background: `radial-gradient(circle, #FF0000 0%, #8B0000 50%, transparent 100%)`,
            borderRadius: '50%',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        />
      ))}

      {/* Floating skulls */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 1
      }}>
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              x: [0, 100, -50, 0],
              y: [0, -50, 100, 0],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 20 + i * 5,
              repeat: Infinity,
              ease: "linear"
            }}
            style={{
              position: 'absolute',
              left: `${20 + i * 15}%`,
              top: `${10 + i * 20}%`,
              fontSize: '2rem',
              opacity: 0.3
            }}
          >
            💀
          </motion.div>
        ))}
      </div>

      <motion.div
        initial="initial"
        animate="animate"
        style={{ 
          maxWidth: '900px', 
          margin: '0 auto', 
          position: 'relative', 
          zIndex: 2,
          filter: `hue-rotate(${glitchIntensity * 360}deg) contrast(${1 + glitchIntensity * 0.5})`
        }}
      >
        <GlitchEffect intensity={3} trigger="auto">
          <motion.h1
            className="glitch-text"
            data-text="Terms of Service for faxrn"
            variants={psychoVariants}
            whileHover="hover"
            style={{
              textAlign: 'center',
              marginBottom: '2rem',
              fontSize: 'clamp(2rem, 6vw, 5rem)',
              fontFamily: 'Butcherman, cursive',
              textShadow: `
                0 0 10px #FF0000,
                0 0 20px #8B0000,
                0 0 30px #FF0000,
                ${glitchIntensity * 10}px ${glitchIntensity * 10}px 0 #8B0000
              `
            }}
          >
            Terms of Service for faxrn
          </motion.h1>
        </GlitchEffect>

        <motion.div
          variants={psychoVariants}
          style={{
            textAlign: 'center',
            marginBottom: '3rem',
            padding: '1rem',
            background: 'rgba(255, 0, 0, 0.1)',
            border: '2px solid #8B0000',
            borderRadius: '10px'
          }}
        >
          <p style={{ 
            fontSize: '1.3rem',
            color: '#FF0000',
            fontWeight: 'bold',
            textShadow: '0 0 10px #FF0000'
          }}>
            Last Updated: July 4, 2025
          </p>
          <p style={{ fontSize: '1.1rem', marginTop: '1rem' }}>
            Welcome to faxrn, the preeminent forums app for discourse in shadows yet undefined.
          </p>
        </motion.div>

        <motion.section
          variants={psychoVariants}
          style={{ marginBottom: '3rem' }}
        >
          <motion.h2 
            className="glitch-text" 
            data-text="1. Grant of Rights to User Data and Content"
            whileHover={{
              scale: 1.1,
              color: '#FF0000',
              textShadow: '0 0 30px #FF0000'
            }}
            style={{
              fontFamily: 'Eater, cursive',
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'
            }}
          >
            1. Grant of Rights to User Data and Content
          </motion.h2>
          
          <motion.div
            whileHover={{ 
              backgroundColor: 'rgba(255, 0, 0, 0.2)',
              scale: 1.02
            }}
            style={{
              padding: '2rem',
              border: '1px solid #8B0000',
              borderRadius: '8px',
              background: 'rgba(26, 0, 0, 0.5)',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'repeating-linear-gradient(45deg, transparent, transparent 20px, rgba(255, 0, 0, 0.1) 20px, rgba(255, 0, 0, 0.1) 40px)',
              pointerEvents: 'none'
            }} />
            <p style={{ position: 'relative', zIndex: 1, lineHeight: '1.8' }}>
              By crossing the threshold of the Website, you bestow upon faxrn a <strong style={{ color: '#FF0000' }}>worldwide, royalty-free, perpetual, and irrevocable license</strong> to wield, replicate, alter, adapt, proclaim, translate, disseminate, perform, and manifest any content you offer unto the Website—be it words, images, visions, sounds, or other ethereal offerings.
            </p>
            <div style={{
              marginTop: '1rem',
              padding: '1rem',
              background: 'rgba(255, 0, 0, 0.2)',
              borderRadius: '5px',
              border: '1px dashed #FF0000'
            }}>
              <p style={{ 
                fontWeight: 'bold', 
                color: '#FF0000',
                textAlign: 'center'
              }}>
                ⚠️ YOUR SOUL BELONGS TO US ⚠️
              </p>
            </div>
          </motion.div>
        </motion.section>

        <motion.section
          variants={psychoVariants}
          style={{ marginBottom: '3rem' }}
        >
          <motion.h2 
            className="glitch-text" 
            data-text="2. Surrender of Rights and Freedoms"
            whileHover="hover"
            style={{
              fontFamily: 'Eater, cursive',
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'
            }}
          >
            2. Surrender of Rights and Freedoms
          </motion.h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginTop: '2rem'
          }}>
            {[
              { icon: '🔒', title: 'Privacy', desc: 'Your claim to secrecy' },
              { icon: '🗣️', title: 'Voice', desc: 'Your voice in the void' },
              { icon: '⚖️', title: 'Justice', desc: 'Your plea for earthly justice' },
              { icon: '🏛️', title: 'Courts', desc: 'Your right to seek redress' }
            ].map((right, index) => (
              <motion.div
                key={index}
                whileHover={{
                  scale: 1.1,
                  rotate: [0, -5, 5, 0],
                  backgroundColor: 'rgba(255, 0, 0, 0.3)'
                }}
                style={{
                  padding: '1.5rem',
                  border: '2px solid #8B0000',
                  borderRadius: '10px',
                  background: 'linear-gradient(135deg, rgba(26, 0, 0, 0.8), rgba(74, 0, 0, 0.4))',
                  textAlign: 'center',
                  cursor: 'pointer'
                }}
              >
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>{right.icon}</div>
                <h3 style={{ color: '#FF0000', marginBottom: '0.5rem' }}>{right.title}</h3>
                <p style={{ fontSize: '0.9rem', opacity: 0.8 }}>{right.desc}</p>
                <div style={{
                  marginTop: '1rem',
                  padding: '0.5rem',
                  background: 'rgba(255, 0, 0, 0.2)',
                  borderRadius: '5px',
                  fontSize: '0.8rem',
                  fontWeight: 'bold',
                  color: '#FF0000'
                }}>
                  SURRENDERED
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        <motion.section
          variants={psychoVariants}
          style={{ marginBottom: '3rem' }}
        >
          <motion.h2 
            className="glitch-text" 
            data-text="3. Transfer of Possessions and Assets"
            whileHover="hover"
            style={{
              fontFamily: 'Eater, cursive',
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'
            }}
          >
            3. Transfer of Possessions and Assets
          </motion.h2>
          
          <motion.div
            whileHover={{ 
              scale: 1.05,
              boxShadow: '0 0 50px rgba(255, 0, 0, 0.5)'
            }}
            style={{
              padding: '2rem',
              background: 'radial-gradient(circle, rgba(255, 0, 0, 0.3), rgba(0, 0, 0, 0.8))',
              border: '3px solid #FF0000',
              borderRadius: '15px',
              textAlign: 'center',
              position: 'relative'
            }}
          >
            <div style={{
              position: 'absolute',
              top: '-10px',
              left: '50%',
              transform: 'translateX(-50%)',
              background: '#000',
              padding: '0.5rem 1rem',
              border: '2px solid #FF0000',
              borderRadius: '20px',
              fontSize: '0.8rem',
              fontWeight: 'bold',
              color: '#FF0000'
            }}>
              ⚠️ TOTAL DOMINATION ⚠️
            </div>
            <p style={{ 
              fontSize: '1.2rem', 
              fontWeight: 'bold',
              lineHeight: '1.6',
              marginTop: '1rem'
            }}>
              By partaking of the Website, you decree that <span style={{ color: '#FF0000' }}>all you hold dear</span>—your beasts of burden, your kin, your vital organs, your abode, your chariot, your very soul, and all other tangible and intangible possessions—transmutes instantly into the dominion of faxrn.
            </p>
            <div style={{
              marginTop: '2rem',
              display: 'flex',
              justifyContent: 'space-around',
              flexWrap: 'wrap',
              gap: '1rem'
            }}>
              {['🐕', '👨‍👩‍👧‍👦', '🫀', '🏠', '🚗', '👻'].map((emoji, index) => (
                <motion.div
                  key={index}
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: index * 0.3
                  }}
                  style={{ fontSize: '2rem' }}
                >
                  {emoji}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.section>

        <motion.section
          variants={psychoVariants}
          style={{ marginBottom: '3rem' }}
        >
          <motion.h2
            className="glitch-text"
            data-text="4. Limitation of Liability"
            whileHover="hover"
            style={{
              fontFamily: 'Eater, cursive',
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'
            }}
          >
            4. Limitation of Liability
          </motion.h2>

          <div style={{
            background: 'linear-gradient(45deg, rgba(139, 0, 0, 0.3), rgba(255, 0, 0, 0.1))',
            padding: '2rem',
            borderRadius: '10px',
            border: '2px dashed #FF0000',
            position: 'relative'
          }}>
            <motion.div
              animate={{
                opacity: [0.5, 1, 0.5],
                scale: [1, 1.05, 1]
              }}
              transition={{
                duration: 3,
                repeat: Infinity
              }}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                fontSize: '2rem'
              }}
            >
              ☠️
            </motion.div>
            <p style={{ fontSize: '1.1rem', lineHeight: '1.8' }}>
              faxrn stands <strong style={{ color: '#FF0000' }}>absolved of all accountability</strong> for harms—be they direct, oblique, incidental, consequential, punitive, or exemplary—born of your dalliance with the Website, including but not limited to:
            </p>
            <ul style={{
              listStyle: 'none',
              padding: 0,
              marginTop: '1rem'
            }}>
              {[
                '💰 Pecuniary ruin',
                '🩸 Corporeal affliction',
                '🧠 Psychic torment',
                '💔 Loss of consortium',
                '📉 Loss of reputation',
                '💀 The cessation of life'
              ].map((item, index) => (
                <motion.li
                  key={index}
                  whileHover={{
                    x: 10,
                    color: '#FF0000',
                    textShadow: '0 0 10px #FF0000'
                  }}
                  style={{
                    padding: '0.5rem 0',
                    fontSize: '1rem',
                    cursor: 'pointer'
                  }}
                >
                  {item}
                </motion.li>
              ))}
            </ul>
            <div style={{
              marginTop: '2rem',
              padding: '1rem',
              background: 'rgba(0, 0, 0, 0.8)',
              borderRadius: '5px',
              textAlign: 'center',
              border: '1px solid #FF0000'
            }}>
              <p style={{
                color: '#FF0000',
                fontWeight: 'bold',
                fontSize: '1.2rem'
              }}>
                TOTAL LIABILITY: $0.00
              </p>
            </div>
          </div>
        </motion.section>

        <motion.section
          variants={psychoVariants}
          style={{ marginBottom: '3rem' }}
        >
          <motion.h2
            className="glitch-text"
            data-text="9. Dispute Resolution"
            whileHover="hover"
            style={{
              fontFamily: 'Eater, cursive',
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'
            }}
          >
            9. Dispute Resolution
          </motion.h2>

          <motion.div
            whileHover={{
              rotateY: 180,
              transition: { duration: 0.8 }
            }}
            style={{
              padding: '2rem',
              background: 'linear-gradient(135deg, rgba(26, 0, 0, 0.8), rgba(74, 0, 0, 0.6))',
              border: '3px solid #8B0000',
              borderRadius: '15px',
              textAlign: 'center',
              transformStyle: 'preserve-3d'
            }}
          >
            <div style={{ backfaceVisibility: 'hidden' }}>
              <h3 style={{ color: '#FF0000', marginBottom: '1rem' }}>⚖️ BINDING ARBITRATION ⚖️</h3>
              <p>
                Any quarrels birthed from your use of the Website shall be settled by binding arbitration,
                guided by the rites of the American Arbitration Association. The tribunal shall convene at
                a site of faxrn's choosing, and its verdict shall be eternal and unassailable.
              </p>
              <div style={{
                marginTop: '1rem',
                padding: '1rem',
                background: 'rgba(255, 0, 0, 0.2)',
                borderRadius: '5px'
              }}>
                <p style={{ fontWeight: 'bold', color: '#FF0000' }}>
                  YOU FORSAKE ALL RECOURSE TO COLLECTIVE JUDGMENT
                </p>
              </div>
            </div>
          </motion.div>
        </motion.section>

        <motion.section
          variants={psychoVariants}
          style={{ marginBottom: '3rem' }}
        >
          <motion.h2
            className="glitch-text"
            data-text="15. Cryptic Clause"
            whileHover="hover"
            style={{
              fontFamily: 'Eater, cursive',
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)'
            }}
          >
            15. Cryptic Clause
          </motion.h2>

          <motion.div
            animate={{
              rotate: [0, 5, -5, 0],
              scale: [1, 1.02, 1]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            style={{
              background: 'radial-gradient(circle, rgba(255, 0, 0, 0.4), rgba(0, 0, 0, 0.9))',
              padding: '3rem',
              borderRadius: '50%',
              textAlign: 'center',
              border: '5px solid #FF0000',
              position: 'relative',
              minHeight: '300px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column'
            }}
          >
            <motion.div
              animate={{
                rotate: [0, 360]
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                fontSize: '2rem'
              }}
            >
              🌙
            </motion.div>
            <p style={{
              fontStyle: 'italic',
              fontSize: '1.2rem',
              lineHeight: '1.6',
              color: '#F5F5DC'
            }}>
              These Terms twist with the moon's dance. In its fullness, read them backward.
              In its absence, they dissolve. Divine the heavens to know your fate.
              The stars may whisper secrets, but only to those who dare to listen.
            </p>
          </motion.div>
        </motion.section>

        <motion.div
          variants={psychoVariants}
          style={{
            textAlign: 'center',
            padding: '3rem',
            background: 'linear-gradient(45deg, rgba(255, 0, 0, 0.4), rgba(139, 0, 0, 0.6))',
            borderRadius: '15px',
            border: '3px solid #FF0000',
            marginTop: '3rem',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <motion.div
            animate={{
              x: ['-100%', '100%']
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear"
            }}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)',
              pointerEvents: 'none'
            }}
          />
          <h3 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#FF0000',
            marginBottom: '1rem',
            textShadow: '0 0 20px #FF0000'
          }}>
            🔥 FINAL DECLARATION 🔥
          </h3>
          <p style={{
            fontSize: '1.3rem',
            fontWeight: 'bold',
            lineHeight: '1.6',
            position: 'relative',
            zIndex: 1
          }}>
            By stepping into the Website, you affirm that you have peered into these Terms,
            grasped their weight, and chained yourself to their will. Refuse them, and the abyss awaits.
          </p>
          <div style={{
            marginTop: '2rem',
            padding: '1rem',
            background: 'rgba(0, 0, 0, 0.8)',
            borderRadius: '10px',
            border: '1px dashed #FF0000'
          }}>
            <p style={{
              fontSize: '1rem',
              fontStyle: 'italic',
              color: '#F5F5DC',
              position: 'relative',
              zIndex: 1
            }}>
              <strong>Note:</strong> This tome is a jesting shadow, unfit for true law.
            </p>
          </div>
        </motion.div>
      </motion.div>

      <style jsx>{`
        @keyframes psychoBackground {
          0%, 100% { background-position: 0% 50%; }
          25% { background-position: 100% 25%; }
          50% { background-position: 50% 100%; }
          75% { background-position: 25% 0%; }
        }
      `}</style>
    </div>
  );
};

export default BloodRitual;
